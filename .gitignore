# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
.DS_Store
dist
dist-ssr
coverage
*.local

/cypress/videos/
/cypress/screenshots/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/launch.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

package-lock.json
yarn.lock

.VSCodeCounter

pnpm-lock.yaml
backend/.vscode/launch.json
tsconfig.tsbuildinfo

src/typings/components.d.ts

*.pyc
backend/logs/*.log*

.idea
.env.bak

.env*
.env.*
.env[0-9]*
.env[0-9][0-9]*

.cursor/*

